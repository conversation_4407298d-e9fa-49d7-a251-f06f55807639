import { getGeneralConfig, updateGeneralConfig } from '../general-config';
import { sendEmail } from './email';
import { storage } from '../storage';

interface TelegramMessage {
  message_id: number;
  from: {
    id: number;
    is_bot: boolean;
    first_name: string;
    username?: string;
  };
  chat: {
    id: number;
    type: string;
  };
  date: number;
  text?: string;
}

interface TelegramUpdate {
  update_id: number;
  message?: TelegramMessage;
  callback_query?: {
    id: string;
    from: {
      id: number;
      first_name: string;
    };
    message: TelegramMessage;
    data: string;
  };
}

interface OrderNotification {
  orderId: number;
  customerName: string;
  customerEmail: string;
  amount: string;
  status: string;
  country: string;
  appType: string;
  isTrialOrder: boolean;
  createdAt: string;
  macAddress?: string;
  checkoutPageName?: string;
}

class TelegramBotService {
  private baseUrl: string = '';
  private config: any = null;
  private pendingM3uRequests: Map<string, { orderId: string; templateId: string; customerEmail: string; customerName: string; m3uLink?: string; username?: string; password?: string; extractedData?: any }> = new Map();
  private pollingInterval: NodeJS.Timeout | null = null;
  private lastUpdateId: number = 0;
  private isPolling: boolean = false;
  private pendingLoginApprovals: Map<string, { username: string; ip: string; userAgent: string; timestamp: number; sessionId: string }> = new Map();
  private pending2FARequests: Map<string, { username: string; sessionId: string; timestamp: number }> = new Map();

  constructor() {
    this.updateConfig();
    this.startPolling();
  }

  private updateConfig() {
    this.config = getGeneralConfig().telegramBot;
    if (this.config.botToken) {
      this.baseUrl = `https://api.telegram.org/bot${this.config.botToken}`;
    }
  }

  /**
   * Start polling for updates
   */
  private async startPolling(): Promise<void> {
    if (this.isPolling) {
      return;
    }

    this.updateConfig();

    if (!this.config.botToken || !this.config.enabled) {
      console.log('Telegram bot polling not started - bot disabled or token missing');
      return;
    }

    this.isPolling = true;
    console.log('🤖 Starting Telegram bot polling...');

    this.pollingInterval = setInterval(async () => {
      try {
        await this.getUpdates();
      } catch (error) {
        console.error('Error during polling:', error);
      }
    }, 1000); // Poll every second
  }

  /**
   * Stop polling for updates
   */
  private stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    this.isPolling = false;
    console.log('🤖 Telegram bot polling stopped');
  }

  /**
   * Get updates from Telegram
   */
  private async getUpdates(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/getUpdates`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          offset: this.lastUpdateId + 1,
          limit: 100,
          timeout: 0,
          allowed_updates: ['message', 'callback_query']
        })
      });

      const data = await response.json();

      if (data.ok && data.result.length > 0) {
        for (const update of data.result) {
          this.lastUpdateId = Math.max(this.lastUpdateId, update.update_id);
          await this.handleUpdate(update);
        }
      }
    } catch (error) {
      console.error('Error getting updates:', error);
    }
  }

  /**
   * Test bot connection
   */
  async testConnection(): Promise<{ success: boolean; message: string; botInfo?: any }> {
    try {
      this.updateConfig();

      if (!this.config.botToken) {
        return { success: false, message: 'Bot token is required' };
      }

      const response = await fetch(`${this.baseUrl}/getMe`);
      const data = await response.json();

      if (data.ok) {
        return {
          success: true,
          message: 'Bot connection successful',
          botInfo: data.result
        };
      } else {
        return {
          success: false,
          message: `Bot error: ${data.description}`
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Set webhook URL
   */
  async setWebhook(webhookUrl: string): Promise<{ success: boolean; message: string }> {
    try {
      this.updateConfig();

      if (!this.config.botToken) {
        return { success: false, message: 'Bot token is required' };
      }

      const response = await fetch(`${this.baseUrl}/setWebhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: webhookUrl,
          allowed_updates: ['message', 'callback_query']
        }),
      });

      const data = await response.json();

      if (data.ok) {
        // Update webhook URL in config
        const generalConfig = getGeneralConfig();
        generalConfig.telegramBot.webhookUrl = webhookUrl;
        updateGeneralConfig(generalConfig);

        return { success: true, message: 'Webhook set successfully' };
      } else {
        return { success: false, message: `Webhook error: ${data.description}` };
      }
    } catch (error) {
      return {
        success: false,
        message: `Webhook setup failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Execute a function with timeout to prevent hanging
   */
  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number = 8000): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) =>
        setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  }

  /**
   * Answer callback query to remove loading state from buttons
   */
  private async answerCallbackQuery(callbackQueryId: string, text?: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/answerCallbackQuery`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          callback_query_id: callbackQueryId,
          text: text || undefined,
          show_alert: false
        })
      });

      const data = await response.json();
      if (!data.ok) {
        console.error('Failed to answer callback query:', data);
      }
    } catch (error) {
      console.error('Error answering callback query:', error);
    }
  }

  /**
   * Send message to admin
   */
  async sendMessage(text: string, replyMarkup?: any): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateConfig();

      if (!this.config.enabled) {
        console.log('Telegram bot is disabled');
        return { success: false, error: 'Telegram bot is disabled. Please enable it in settings.' };
      }

      if (!this.config.botToken) {
        console.log('Telegram bot token not configured');
        return { success: false, error: 'Bot token is not configured. Please add your bot token.' };
      }

      if (!this.config.adminChatId) {
        console.log('Telegram admin chat ID not configured');
        return { success: false, error: 'Admin Chat ID is not configured. Please start your bot and get your Chat ID.' };
      }

      console.log(`Sending Telegram message to chat ID: ${this.config.adminChatId}`);

      const response = await fetch(`${this.baseUrl}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: this.config.adminChatId,
          text: text,
          parse_mode: 'HTML',
          reply_markup: replyMarkup
        }),
      });

      const data = await response.json();

      if (data.ok) {
        console.log('Telegram message sent successfully');
        return { success: true };
      } else {
        console.error('Telegram API error:', data);
        return { success: false, error: `Telegram API error: ${data.description || 'Unknown error'}` };
      }
    } catch (error) {
      console.error('Failed to send Telegram message:', error);
      return { success: false, error: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}` };
    }
  }

  /**
   * Send order notification
   */
  async sendOrderNotification(order: OrderNotification): Promise<boolean> {
    try {
      this.updateConfig();

      if (!this.config.notifications.newOrders) {
        return false;
      }

      const orderType = order.isTrialOrder ? '🎯 TRIAL' : '💎 PREMIUM';
      const statusEmoji = order.status === 'paid' ? '✅' : order.status === 'sent' ? '📧' : '⏳';

      let message = `🔔 <b>NEW ORDER RECEIVED!</b>\n\n`;
      message += `${orderType} ORDER #${order.orderId}\n`;
      message += `${statusEmoji} Status: ${order.status.toUpperCase()}\n\n`;
      message += `👤 <b>Customer:</b> ${order.customerName}\n`;
      message += `📧 <b>Email:</b> ${order.customerEmail}\n`;
      message += `🌍 <b>Country:</b> ${order.country}\n`;
      message += `📱 <b>App Type:</b> ${order.appType}\n`;
      message += `💰 <b>Amount:</b> $${order.amount}\n`;

      if (order.checkoutPageName) {
        message += `📄 <b>Checkout Page:</b> ${order.checkoutPageName}\n`;
      }

      if (order.macAddress) {
        message += `🔧 <b>MAC Address:</b> ${order.macAddress}\n`;
      }

      message += `📅 <b>Date:</b> ${new Date(order.createdAt).toLocaleString()}\n`;

      // Add action buttons
      const replyMarkup = {
        inline_keyboard: [
          [
            { text: '📧 Choose Template', callback_data: `choose_template_${order.orderId}` },
            { text: '👀 View Details', callback_data: `view_order_${order.orderId}` }
          ],
          [
            { text: '🔗 Send M3U Link', callback_data: `send_m3u_${order.orderId}` },
            { text: order.status === 'paid' ? '✅ Paid' : '💰 Mark as Paid', callback_data: order.status === 'paid' ? 'already_paid' : `mark_paid_${order.orderId}` }
          ],
          [
            { text: '📊 Order Stats', callback_data: 'order_stats' }
          ]
        ]
      };

      const result = await this.sendMessage(message, replyMarkup);
      return result.success;
    } catch (error) {
      console.error('Failed to send order notification:', error);
      return false;
    }
  }

  /**
   * Send system monitoring message
   */
  async sendSystemMessage(message: string): Promise<boolean> {
    try {
      this.updateConfig();

      if (!this.config.enabled) {
        console.log('Telegram bot is disabled, skipping system message');
        return false;
      }

      if (!this.config.botToken || !this.config.adminChatId) {
        console.log('Telegram bot not properly configured, skipping system message');
        return false;
      }

      const result = await this.sendMessage(message);
      return result.success;
    } catch (error) {
      console.error('Failed to send system message:', error);
      return false;
    }
  }

  /**
   * Handle incoming webhook updates
   */
  async handleUpdate(update: TelegramUpdate): Promise<void> {
    try {
      this.updateConfig();

      // Handle callback queries (button presses)
      if (update.callback_query) {
        await this.handleCallbackQuery(update.callback_query);
        return;
      }

      // Handle text messages
      if (update.message && update.message.text) {
        await this.handleTextMessage(update.message);
      }
    } catch (error) {
      console.error('Error handling Telegram update:', error);
    }
  }

  /**
   * Handle callback queries (button presses)
   */
  private async handleCallbackQuery(callbackQuery: any): Promise<void> {
    const data = callbackQuery.data;
    const chatId = callbackQuery.message.chat.id;
    const callbackQueryId = callbackQuery.id;

    // CRITICAL: Check for duplicate callback queries to prevent infinite loops
    if (this.processedCallbackQueries.has(callbackQueryId)) {
      console.log(`⚠️ Callback query ${callbackQueryId} already processed, skipping to prevent spam`);
      return;
    }

    // Mark as processed immediately
    this.processedCallbackQueries.add(callbackQueryId);

    // CRITICAL: Answer callback query IMMEDIATELY to prevent button freezing
    // This must be done first, regardless of what happens in the handlers
    try {
      await this.answerCallbackQuery(callbackQueryId);
      console.log(`✅ Answered callback query: ${callbackQueryId}`);
    } catch (error) {
      console.error('Failed to answer callback query:', error);
      return; // Don't process if we can't answer
    }

    try {
      // Verify admin access
      if (this.config.security.verifyAdminOnly && chatId.toString() !== this.config.adminChatId) {
        await this.sendMessage('❌ Unauthorized access');
        return;
      }

      // Additional protection: Check for recent duplicate actions
      const actionKey = `${data}_${Math.floor(Date.now() / 1000)}`; // Group by second
      if (this.processedCallbackQueries.has(actionKey)) {
        console.log(`⚠️ Action ${data} recently processed, skipping to prevent spam`);
        return;
      }
      this.processedCallbackQueries.add(actionKey);

      console.log(`🔄 Processing callback query: ${data}`);

      // Handle different callback data types with timeout protection
      if (data.startsWith('send_email_')) {
        const orderId = data.replace('send_email_', '');
        await this.withTimeout(this.handleSendEmailRequest(orderId));
      } else if (data.startsWith('choose_template_')) {
        const orderId = data.replace('choose_template_', '');
        await this.withTimeout(this.handleChooseTemplate(orderId));
      } else if (data.startsWith('view_order_')) {
        const orderId = data.replace('view_order_', '');
        await this.withTimeout(this.handleViewOrderRequest(orderId));
      } else if (data.startsWith('send_m3u_') || data.startsWith('add_m3u_')) {
        const orderId = data.replace('send_m3u_', '').replace('add_m3u_', '');
        await this.withTimeout(this.handleSendM3uRequest(orderId));
      } else if (data.startsWith('select_template_')) {
        const parts = data.split('_');
        const orderId = parts[2];
        const templateId = parts[3];
        await this.withTimeout(this.handleTemplateSelection(orderId, templateId));
      } else if (data === 'cancel_template_selection') {
        await this.withTimeout(this.sendMessage('❌ Template selection cancelled.'));
      } else if (data.startsWith('send_email_now_')) {
        const orderId = data.replace('send_email_now_', '');
        await this.withTimeout(this.handleSendEmailNow(orderId));
      } else if (data === 'cancel_email_send') {
        await this.withTimeout(this.handleCancelEmailSend());
      } else if (data.startsWith('preview_email_')) {
        const orderId = data.replace('preview_email_', '');
        await this.withTimeout(this.handlePreviewEmail(orderId));
      } else if (data.startsWith('confirm_send_')) {
        const orderId = data.replace('confirm_send_', '');
        await this.withTimeout(this.handleConfirmSend(orderId));
      } else if (data.startsWith('mark_paid_')) {
        const orderId = parseInt(data.replace('mark_paid_', ''));
        console.log(`🔄 Processing mark as paid for order: ${orderId}`);
        await this.withTimeout(this.handleMarkAsPaid(orderId));
      } else if (data === 'already_paid') {
        await this.withTimeout(this.sendMessage('✅ This order is already marked as paid.'));
      } else if (data === 'order_stats') {
        await this.withTimeout(this.handleOrderStatsRequest());
      } else if (data === 'clear_cache') {
        await this.withTimeout(this.handleClearCacheCallback());
      } else if (data === 'reset_bot') {
        await this.withTimeout(this.handleResetBotCallback());
      } else if (data === 'list_templates') {
        await this.withTimeout(this.handleListTemplatesCallback());
      } else if (data === 'recent_orders') {
        await this.withTimeout(this.handleRecentOrdersCallback());
      } else if (data.startsWith('approve_login_')) {
        const approvalId = data.replace('approve_login_', '');
        await this.withTimeout(this.handleLoginApproval(approvalId, true));
      } else if (data.startsWith('deny_login_')) {
        const approvalId = data.replace('deny_login_', '');
        await this.withTimeout(this.handleLoginApproval(approvalId, false));
      } else if (data.startsWith('approve_2fa_')) {
        const requestId = data.replace('approve_2fa_', '');
        await this.withTimeout(this.handle2FAApproval(requestId, true));
      } else if (data.startsWith('deny_2fa_')) {
        const requestId = data.replace('deny_2fa_', '');
        await this.withTimeout(this.handle2FAApproval(requestId, false));
      } else {
        console.log(`Unknown callback data: ${data}`);
      }
    } catch (error) {
      console.error('Error handling callback query:', error);
      // Send error message to user
      try {
        await this.sendMessage('❌ An error occurred while processing your request. Please try again.');
      } catch (sendError) {
        console.error('Failed to send error message:', sendError);
      }
    }
  }

  /**
   * Handle text messages
   */
  private async handleTextMessage(message: TelegramMessage): Promise<void> {
    const text = message.text || '';
    const chatId = message.chat.id;

    // Verify admin access
    if (this.config.security.verifyAdminOnly && chatId.toString() !== this.config.adminChatId) {
      await this.sendMessage('❌ Unauthorized access. Please contact the administrator.');
      return;
    }

    // Check if there's a pending M3U request
    const pendingRequest = this.pendingM3uRequests.get(chatId.toString());
    if (pendingRequest && !text.startsWith('/')) {
      await this.handleM3uLinkSubmission(chatId.toString(), text, pendingRequest);
      return;
    }

    if (text.startsWith('/start')) {
      await this.handleStartCommand(chatId);
    } else if (text.startsWith('/help')) {
      await this.handleHelpCommand();
    } else if (text.startsWith('/orders')) {
      await this.handleOrdersCommand();
    } else if (text.startsWith('/templates')) {
      await this.handleTemplatesCommand();
    } else if (text.startsWith('/status')) {
      await this.handleStatusCommand();
    } else if (text.startsWith('/test')) {
      await this.handleTestCommand();
    } else if (text.startsWith('/system')) {
      await this.handleSystemCommand();
    } else if (text.startsWith('/monitor')) {
      await this.handleMonitorCommand();
    } else if (text.startsWith('/errors')) {
      await this.handleErrorsCommand();
    } else if (text.startsWith('/cache')) {
      await this.handleCacheCommand();
    } else if (text.startsWith('/reset')) {
      await this.handleResetCommand();
    }
  }

  /**
   * Handle /start command
   */
  private async handleStartCommand(chatId: number): Promise<void> {
    const message = `🤖 <b>Welcome to Your Store Bot!</b>\n\n` +
      `Your Chat ID: <code>${chatId}</code>\n\n` +
      `📋 <b>Available Commands:</b>\n` +
      `/orders - View recent orders\n` +
      `/templates - List email templates\n` +
      `/status - Check bot status\n` +
      `/test - Send test notification\n` +
      `/system - Get system performance report\n` +
      `/monitor - Toggle system monitoring\n` +
      `/errors - View recent error logs\n` +
      `/help - Show this help message\n\n` +
      `💡 <i>Copy your Chat ID and paste it in the admin dashboard to complete setup.</i>`;

    // Send message directly to this chat ID
    try {
      const response = await fetch(`${this.baseUrl}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chatId,
          text: message,
          parse_mode: 'HTML'
        }),
      });

      const data = await response.json();
      if (data.ok) {
        console.log(`Start message sent successfully to chat ID: ${chatId}`);
      } else {
        console.error('Failed to send start message:', data);
      }
    } catch (error) {
      console.error('Error sending start message:', error);
    }
  }

  /**
   * Handle /help command
   */
  private async handleHelpCommand(): Promise<void> {
    const message = `🆘 <b>Bot Help</b>\n\n` +
      `<b>Commands:</b>\n` +
      `• /start - Get your Chat ID\n` +
      `• /orders - View recent orders\n` +
      `• /templates - List email templates\n` +
      `• /status - Check bot status\n` +
      `• /test - Send test notification\n` +
      `• /system - Get system performance report\n` +
      `• /monitor - Toggle system monitoring\n` +
      `• /errors - View recent error logs\n` +
      `• /help - Show this help\n\n` +
      `<b>Features:</b>\n` +
      `• 🔔 Instant order notifications\n` +
      `• 📧 Send emails via templates\n` +
      `• 🔗 M3U link management\n` +
      `• 📊 Order statistics\n\n` +
      `<i>Use the buttons in notifications for quick actions!</i>`;

    await this.sendMessage(message);
  }

  /**
   * Handle /orders command
   */
  private async handleOrdersCommand(): Promise<void> {
    try {
      const orders = await storage.getInvoices();
      const recentOrders = orders.slice(-5).reverse(); // Last 5 orders

      if (recentOrders.length === 0) {
        await this.sendMessage('📭 No orders found.');
        return;
      }

      let message = `📋 <b>Recent Orders (Last 5)</b>\n\n`;

      recentOrders.forEach((order, index) => {
        const orderType = order.isTrialOrder ? '🎯' : '💎';
        const statusEmoji = order.status === 'paid' ? '✅' : order.status === 'sent' ? '📧' : '⏳';

        message += `${orderType} <b>Order #${order.id}</b>\n`;
        message += `${statusEmoji} ${order.status.toUpperCase()} - $${order.amount}\n`;
        message += `👤 ${order.customerName} (${order.customerEmail})\n`;
        message += `📅 ${new Date(order.createdAt).toLocaleDateString()}\n\n`;
      });

      await this.sendMessage(message);
    } catch (error) {
      await this.sendMessage('❌ Failed to fetch orders.');
    }
  }

  /**
   * Handle /templates command
   */
  private async handleTemplatesCommand(): Promise<void> {
    const message = `📨 <b>Available Email Templates</b>\n\n` +
      `• Payment Confirmation\n` +
      `• Welcome Email\n` +
      `• Trial Activation\n` +
      `• Upgrade Notification\n` +
      `• Support Response\n` +
      `• Custom Message\n\n` +
      `💡 <i>Use the "Send Email" button in order notifications to send emails quickly!</i>`;

    await this.sendMessage(message);
  }

  /**
   * Handle /status command
   */
  private async handleStatusCommand(): Promise<void> {
    this.updateConfig();

    const status = this.config.enabled ? '🟢 Active' : '🔴 Disabled';
    const notifications = this.config.notifications.newOrders ? '🔔 Enabled' : '🔕 Disabled';
    const emailIntegration = this.config.emailIntegration.enabled ? '📧 Enabled' : '📧 Disabled';
    const m3uManagement = this.config.m3uManagement.enabled ? '🔗 Enabled' : '🔗 Disabled';

    const message = `📊 <b>Bot Status</b>\n\n` +
      `Status: ${status}\n` +
      `Notifications: ${notifications}\n` +
      `Email Integration: ${emailIntegration}\n` +
      `M3U Management: ${m3uManagement}\n` +
      `Admin Chat ID: ${this.config.adminChatId}\n\n` +
      `🕐 Last updated: ${new Date().toLocaleString()}`;

    await this.sendMessage(message);
  }

  /**
   * Handle /test command
   */
  private async handleTestCommand(): Promise<void> {
    const testOrder: OrderNotification = {
      orderId: 9999,
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      amount: '19.99',
      status: 'paid',
      country: 'Test Country',
      appType: 'IPTV Smarters Pro',
      isTrialOrder: false,
      createdAt: new Date().toISOString(),
      macAddress: '00:1A:79:B4:E7:2D',
      checkoutPageName: 'Test Checkout Page'
    };

    const success = await this.sendOrderNotification(testOrder);

    if (success) {
      await this.sendMessage('✅ Test notification sent successfully!');
    } else {
      await this.sendMessage('❌ Failed to send test notification.');
    }
  }

  /**
   * Handle send email request
   */
  private async handleSendEmailRequest(orderId: string): Promise<void> {
    await this.sendMessage(`📧 Email sending feature for Order #${orderId} will be implemented next!`);
  }

  /**
   * Handle view order request
   */
  private async handleViewOrderRequest(orderId: string): Promise<void> {
    try {
      const orders = await storage.getInvoices();
      const order = orders.find(o => o.id.toString() === orderId);

      if (!order) {
        await this.sendMessage('❌ Order not found.');
        return;
      }

      const orderType = order.isTrialOrder ? '🎯 TRIAL' : '💎 PREMIUM';
      const statusEmoji = order.status === 'paid' ? '✅' : order.status === 'sent' ? '📧' : '⏳';

      let message = `📋 <b>Order Details #${order.id}</b>\n\n`;
      message += `${orderType} ORDER\n`;
      message += `${statusEmoji} Status: ${order.status.toUpperCase()}\n\n`;
      message += `👤 <b>Customer:</b> ${order.customerName}\n`;
      message += `📧 <b>Email:</b> ${order.customerEmail}\n`;
      message += `🌍 <b>Country:</b> ${order.country}\n`;
      message += `📱 <b>App Type:</b> ${order.appType}\n`;
      message += `💰 <b>Amount:</b> $${order.amount}\n`;

      if (order.macAddress) {
        message += `🔧 <b>MAC Address:</b> ${order.macAddress}\n`;
      }

      if (order.notes) {
        message += `📝 <b>Notes:</b> ${order.notes}\n`;
      }

      message += `📅 <b>Created:</b> ${new Date(order.createdAt).toLocaleString()}\n`;

      await this.sendMessage(message);
    } catch (error) {
      await this.sendMessage('❌ Failed to fetch order details.');
    }
  }

  /**
   * Handle choose template - your main business workflow
   */
  private async handleChooseTemplate(orderId: string): Promise<void> {
    try {
      // Get available email templates
      const templates = await storage.getEmailTemplates();

      if (templates.length === 0) {
        await this.sendMessage('❌ No email templates found. Please create templates in the admin dashboard first.');
        return;
      }

      // Get order details
      const orders = await storage.getInvoices();
      const order = orders.find(o => o.id.toString() === orderId);

      if (!order) {
        await this.sendMessage('❌ Order not found.');
        return;
      }

      let message = `📧 <b>Choose Email Template</b>\n\n`;
      message += `📋 <b>Order #${orderId}</b>\n`;
      message += `👤 Customer: ${order.customerName}\n`;
      message += `📧 Email: ${order.customerEmail}\n\n`;
      message += `Select a template to send the M3U credentials:\n`;

      // Create inline keyboard with template options
      const keyboard = [];

      for (const template of templates) {
        keyboard.push([{
          text: `📄 ${template.name}`,
          callback_data: `select_template_${orderId}_${template.id}`
        }]);
      }

      // Add cancel button
      keyboard.push([{ text: '❌ Cancel', callback_data: 'cancel_template_selection' }]);

      const replyMarkup = { inline_keyboard: keyboard };
      await this.sendMessage(message, replyMarkup);
    } catch (error) {
      console.error('Error showing template selection:', error);
      await this.sendMessage('❌ Failed to load email templates.');
    }
  }

  /**
   * Handle send M3U request - show template selection
   */
  private async handleSendM3uRequest(orderId: string): Promise<void> {
    try {
      // Get available email templates
      const templates = await storage.getEmailTemplates();

      if (templates.length === 0) {
        await this.sendMessage('❌ No email templates found. Please create templates in the admin dashboard first.');
        return;
      }

      let message = `📧 <b>Select Email Template for Order #${orderId}</b>\n\n`;
      message += `Choose a template to send the M3U link:\n\n`;

      // Create inline keyboard with template options
      const keyboard = [];
      const templatesPerRow = 1; // One template per row for better readability

      for (let i = 0; i < templates.length; i += templatesPerRow) {
        const row = [];
        for (let j = 0; j < templatesPerRow && i + j < templates.length; j++) {
          const template = templates[i + j];
          row.push({
            text: `📄 ${template.name}`,
            callback_data: `select_template_${orderId}_${template.id}`
          });
        }
        keyboard.push(row);
      }

      // Add cancel button
      keyboard.push([{ text: '❌ Cancel', callback_data: 'cancel_template_selection' }]);

      const replyMarkup = { inline_keyboard: keyboard };
      await this.sendMessage(message, replyMarkup);
    } catch (error) {
      console.error('Error showing template selection:', error);
      await this.sendMessage('❌ Failed to load email templates.');
    }
  }

  /**
   * Handle template selection and M3U link input
   */
  private async handleTemplateSelection(orderId: string, templateId: string): Promise<void> {
    try {
      // Get the selected template
      const templates = await storage.getEmailTemplates();
      const template = templates.find(t => t.id.toString() === templateId);

      if (!template) {
        await this.sendMessage('❌ Template not found.');
        return;
      }

      // Get order details
      const orders = await storage.getInvoices();
      const order = orders.find(o => o.id.toString() === orderId);

      if (!order) {
        await this.sendMessage('❌ Order not found.');
        return;
      }

      let message = `✅ <b>Template Selected: ${template.name}</b>\n\n`;
      message += `📋 <b>Order #${orderId}</b>\n`;
      message += `👤 Customer: ${order.customerName}\n`;
      message += `📧 Email: ${order.customerEmail}\n\n`;
      message += `🔗 <b>Now paste the M3U link for this customer:</b>\n\n`;
      message += `💡 <i>Send the M3U link and I'll extract the username/password and show you the email preview!</i>`;

      // Store the pending M3U request
      const chatId = this.config.adminChatId;
      this.pendingM3uRequests.set(chatId, {
        orderId,
        templateId,
        customerEmail: order.customerEmail,
        customerName: order.customerName,
        templateName: template.name
      });

      await this.sendMessage(message);
    } catch (error) {
      console.error('Error handling template selection:', error);
      await this.sendMessage('❌ Failed to process template selection.');
    }
  }

  /**
   * Extract username and password from M3U link
   */
  private extractCredentialsFromM3U(m3uLink: string): { username?: string; password?: string; serverUrl?: string } {
    try {
      const url = new URL(m3uLink);
      const params = url.searchParams;

      // Common parameter names for username and password
      const username = params.get('username') || params.get('user') || params.get('u');
      const password = params.get('password') || params.get('pass') || params.get('p');

      // Extract server URL (base URL without parameters)
      const serverUrl = `${url.protocol}//${url.host}${url.pathname}`.replace('/get.php', '');

      return { username, password, serverUrl };
    } catch (error) {
      console.error('Error extracting credentials from M3U link:', error);
      return {};
    }
  }

  /**
   * Handle M3U link submission
   */
  private async handleM3uLinkSubmission(chatId: string, m3uLink: string, pendingRequest: { orderId: string; templateId: string; customerEmail: string; customerName: string; templateName?: string }): Promise<void> {
    try {
      // Validate M3U link format (basic validation)
      if (!m3uLink.trim() || (!m3uLink.includes('http') && !m3uLink.includes('.m3u'))) {
        await this.sendMessage('❌ Invalid M3U link format. Please provide a valid M3U URL or link.');
        return;
      }

      // Extract credentials from M3U link
      const credentials = this.extractCredentialsFromM3U(m3uLink);

      // Update pending request with M3U link and extracted credentials
      const updatedRequest = {
        ...pendingRequest,
        m3uLink,
        username: credentials.username,
        password: credentials.password,
        serverUrl: credentials.serverUrl
      };
      this.pendingM3uRequests.set(chatId, updatedRequest);

      // Show extracted information and preview button
      let message = `✅ <b>M3U Link Processed Successfully!</b>\n\n`;
      message += `📋 <b>Order #${pendingRequest.orderId}</b>\n`;
      message += `👤 <b>Customer:</b> ${pendingRequest.customerName}\n`;
      message += `📧 <b>Email:</b> ${pendingRequest.customerEmail}\n`;
      message += `📄 <b>Template:</b> ${pendingRequest.templateName}\n\n`;

      if (credentials.username && credentials.password) {
        message += `🔑 <b>Extracted Credentials:</b>\n`;
        message += `👤 Username: <code>${credentials.username}</code>\n`;
        message += `🔒 Password: <code>${credentials.password}</code>\n`;
        if (credentials.serverUrl) {
          message += `🌐 Server: <code>${credentials.serverUrl}</code>\n`;
        }
        message += `🔗 M3U Link: <code>${m3uLink.substring(0, 50)}${m3uLink.length > 50 ? '...' : ''}</code>\n\n`;
        message += `✅ <b>Ready to preview and send!</b>`;
      } else {
        message += `⚠️ <b>Could not extract credentials automatically</b>\n`;
        message += `🔗 M3U Link: <code>${m3uLink.substring(0, 50)}${m3uLink.length > 50 ? '...' : ''}</code>\n\n`;
        message += `The M3U link will be included in the email as-is.`;
      }

      // Add Preview and Send buttons
      const replyMarkup = {
        inline_keyboard: [
          [
            { text: '👀 Preview Email', callback_data: `preview_email_${pendingRequest.orderId}` },
            { text: '📧 Send Now', callback_data: `confirm_send_${pendingRequest.orderId}` }
          ],
          [
            { text: '❌ Cancel', callback_data: 'cancel_email_send' }
          ]
        ]
      };

      await this.sendMessage(message, replyMarkup);
    } catch (error) {
      console.error('Error handling M3U link submission:', error);
      await this.sendMessage('❌ Failed to process M3U link.');
      this.pendingM3uRequests.delete(chatId);
    }
  }

  /**
   * Handle send email now button click
   */
  private async handleSendEmailNow(orderId: string): Promise<void> {
    try {
      const chatId = this.config.adminChatId;
      const pendingRequest = this.pendingM3uRequests.get(chatId);

      if (!pendingRequest || pendingRequest.orderId !== orderId) {
        await this.sendMessage('❌ No pending email request found for this order.');
        return;
      }

      // Get the template
      const templates = await storage.getEmailTemplates();
      const template = templates.find(t => t.id.toString() === pendingRequest.templateId);

      if (!template) {
        await this.sendMessage('❌ Template not found.');
        this.pendingM3uRequests.delete(chatId);
        return;
      }

      await this.sendMessage('📧 Sending email...');

      try {
        // Import email service
        const { sendEmail } = await import('./email');

        // Use text content for clean formatting (naked M3U links)
        let emailContent = template.textContent || template.htmlContent || template.content || '';
        let emailSubject = template.subject || 'Your IPTV Subscription Details';

        // Remove any hardcoded subject additions - use only the template subject

        // Replace placeholders
        emailContent = emailContent.replace(/\{\{customerName\}\}/g, pendingRequest.customerName);
        emailContent = emailContent.replace(/\{\{orderId\}\}/g, pendingRequest.orderId);
        emailContent = emailContent.replace(/\{\{m3uLink\}\}/g, pendingRequest.m3uLink || '');

        // Replace credential placeholders if available
        if (pendingRequest.username) {
          emailContent = emailContent.replace(/\{\{username\}\}/g, pendingRequest.username);
        }
        if (pendingRequest.password) {
          emailContent = emailContent.replace(/\{\{password\}\}/g, pendingRequest.password);
        }
        if (pendingRequest.serverUrl) {
          emailContent = emailContent.replace(/\{\{serverUrl\}\}/g, pendingRequest.serverUrl);
        }

        emailSubject = emailSubject.replace(/\{\{customerName\}\}/g, pendingRequest.customerName);
        emailSubject = emailSubject.replace(/\{\{orderId\}\}/g, pendingRequest.orderId);

        // The template already has the correct format with naked M3U link and placeholders
        // Just let the template placeholders be replaced - no need to override the template format

        // Get the order to find the SMTP provider from the custom checkout page
        let smtpProviderId = undefined;
        try {
          const order = await storage.getInvoice(parseInt(pendingRequest.orderId));
          if (order && order.customCheckoutPageId) {
            const checkoutPage = await storage.getCustomCheckoutPage(order.customCheckoutPageId);
            if (checkoutPage && checkoutPage.smtpProviderId) {
              smtpProviderId = checkoutPage.smtpProviderId;
              console.log(`Using SMTP provider from checkout page: ${smtpProviderId}`);
            }
          }
        } catch (error) {
          console.warn('Could not retrieve SMTP provider from checkout page:', error);
        }

        // Create HTML version with proper formatting (no Telegram signature)
        const htmlContent = emailContent.replace(/\n/g, '<br>');

        // Send the email using the selected SMTP provider and clean content (no Telegram mentions)
        // Create transporter with the specific SMTP provider
        let transporter;
        if (smtpProviderId) {
          const { createTransporter } = await import('./email');
          transporter = createTransporter(smtpProviderId);
        } else {
          const { createTransporter } = await import('./email');
          transporter = createTransporter();
        }

        // Get email credentials from the provider
        const emailConfig = await import('../config-storage').then(m => m.getEmailConfig());
        let fromEmail = '<EMAIL>';
        let fromName = 'PayPal Invoicer';

        if (smtpProviderId) {
          const provider = emailConfig.providers.find(p => p.id === smtpProviderId && p.active);
          if (provider && provider.credentials) {
            fromEmail = (provider.credentials as any).fromEmail || fromEmail;
            fromName = (provider.credentials as any).fromName || fromName;
          }
        }

        const mailOptions = {
          from: `"${fromName}" <${fromEmail}>`,
          to: pendingRequest.customerEmail,
          subject: emailSubject,
          text: emailContent,
          html: htmlContent
        };

        const result = await transporter.sendMail(mailOptions);

        if (result && result.messageId) {
          // Update order notes to track that email was sent (without Telegram mentions)
          try {
            const order = await storage.getInvoice(parseInt(pendingRequest.orderId));
            if (order) {
              const emailNote = `Email sent on ${new Date().toISOString()} - Subject: ${emailSubject}`;
              const updatedNotes = order.notes ? `${order.notes}\n\n${emailNote}` : emailNote;
              await storage.updateInvoice(parseInt(pendingRequest.orderId), { notes: updatedNotes });
              console.log('📝 Updated order notes with email tracking');
            }
          } catch (noteError) {
            console.error('⚠️ Failed to update order notes:', noteError);
          }

          // Note: Email will be added to Allowed Emails when order is marked as paid

          let successMessage = `✅ <b>Email sent successfully!</b>\n\n`;
          successMessage += `📧 <b>To:</b> ${pendingRequest.customerEmail}\n`;
          successMessage += `📋 <b>Order:</b> #${pendingRequest.orderId}\n`;
          successMessage += `📄 <b>Template:</b> ${pendingRequest.templateName}\n`;
          successMessage += `🕐 <b>Sent at:</b> ${new Date().toLocaleString()}\n`;
          if (pendingRequest.username && pendingRequest.password) {
            successMessage += `🔑 <b>Credentials:</b> ${pendingRequest.username} / ${pendingRequest.password}\n`;
          }
          successMessage += `🔗 <b>M3U Link:</b> Included\n`;
          successMessage += `📧 <b>Subject:</b> ${emailSubject}\n`;
          if (smtpProviderId) {
            successMessage += `📤 <b>SMTP Provider:</b> ${smtpProviderId}\n`;
          }
          successMessage += `💡 <b>Tip:</b> Mark order as paid to add email to Allowed Emails`;

          await this.sendMessage(successMessage);
        } else {
          await this.sendMessage(`❌ Failed to send email: Unknown error`);
        }
      } catch (emailError) {
        console.error('Error sending M3U email:', emailError);
        await this.sendMessage('❌ Failed to send email. Please check your email configuration.');
      }

      // Clear the pending request
      this.pendingM3uRequests.delete(chatId);
    } catch (error) {
      console.error('Error handling send email now:', error);
      await this.sendMessage('❌ Failed to send email.');
    }
  }

  /**
   * Handle preview email - show what will be sent
   */
  private async handlePreviewEmail(orderId: string): Promise<void> {
    try {
      const chatId = this.config.adminChatId;
      const pendingRequest = this.pendingM3uRequests.get(chatId);

      if (!pendingRequest || pendingRequest.orderId !== orderId) {
        await this.sendMessage('❌ No pending email request found for this order.');
        return;
      }

      // Get the template
      const templates = await storage.getEmailTemplates();
      const template = templates.find(t => t.id.toString() === pendingRequest.templateId);

      if (!template) {
        await this.sendMessage('❌ Template not found.');
        return;
      }

      // Use text content for clean formatting preview
      let emailContent = template.textContent || template.htmlContent || template.content || '';
      let emailSubject = template.subject || 'Your IPTV Subscription Details';

      // Replace placeholders
      emailContent = emailContent.replace(/\{\{customerName\}\}/g, pendingRequest.customerName);
      emailContent = emailContent.replace(/\{\{orderId\}\}/g, pendingRequest.orderId);
      emailContent = emailContent.replace(/\{\{m3uLink\}\}/g, pendingRequest.m3uLink || '');

      // Replace credential placeholders if available
      if (pendingRequest.username) {
        emailContent = emailContent.replace(/\{\{username\}\}/g, pendingRequest.username);
      }
      if (pendingRequest.password) {
        emailContent = emailContent.replace(/\{\{password\}\}/g, pendingRequest.password);
      }
      if (pendingRequest.serverUrl) {
        emailContent = emailContent.replace(/\{\{serverUrl\}\}/g, pendingRequest.serverUrl);
      }

      emailSubject = emailSubject.replace(/\{\{customerName\}\}/g, pendingRequest.customerName);
      emailSubject = emailSubject.replace(/\{\{orderId\}\}/g, pendingRequest.orderId);

      // Create preview message
      let previewMessage = `📧 <b>EMAIL PREVIEW</b>\n\n`;
      previewMessage += `📧 <b>To:</b> ${pendingRequest.customerEmail}\n`;
      previewMessage += `📋 <b>Subject:</b> ${emailSubject}\n\n`;
      previewMessage += `📄 <b>Content Preview:</b>\n`;

      // Clean up text content for preview
      let textPreview = emailContent
        .replace(/\n\s*\n/g, '\n') // Remove extra line breaks
        .trim();

      // Limit preview length
      if (textPreview.length > 500) {
        textPreview = textPreview.substring(0, 500) + '...';
      }

      previewMessage += `<code>${textPreview}</code>\n\n`;

      if (pendingRequest.username && pendingRequest.password) {
        previewMessage += `🔑 <b>Credentials included:</b>\n`;
        previewMessage += `👤 Username: ${pendingRequest.username}\n`;
        previewMessage += `🔒 Password: ${pendingRequest.password}\n`;
      }

      // Add action buttons
      const replyMarkup = {
        inline_keyboard: [
          [
            { text: '📧 Send This Email', callback_data: `confirm_send_${orderId}` },
            { text: '❌ Cancel', callback_data: 'cancel_email_send' }
          ]
        ]
      };

      await this.sendMessage(previewMessage, replyMarkup);
    } catch (error) {
      console.error('Error showing email preview:', error);
      await this.sendMessage('❌ Failed to generate email preview.');
    }
  }

  /**
   * Handle confirm send - actually send the email
   */
  private async handleConfirmSend(orderId: string): Promise<void> {
    // Use the existing handleSendEmailNow logic
    await this.handleSendEmailNow(orderId);
  }

  /**
   * Handle cancel email send
   */
  private async handleCancelEmailSend(): Promise<void> {
    const chatId = this.config.adminChatId;
    this.pendingM3uRequests.delete(chatId);
    await this.sendMessage('❌ Email sending cancelled.');
  }

  /**
   * Handle order stats request
   */
  private async handleOrderStatsRequest(): Promise<void> {
    try {
      const orders = await storage.getInvoices();
      const totalOrders = orders.length;
      const paidOrders = orders.filter(o => o.status === 'paid').length;
      const trialOrders = orders.filter(o => o.isTrialOrder).length;
      const totalRevenue = orders
        .filter(o => o.status === 'paid')
        .reduce((sum, o) => sum + parseFloat(o.amount), 0);

      const message = `📊 <b>Order Statistics</b>\n\n` +
        `📦 Total Orders: ${totalOrders}\n` +
        `✅ Paid Orders: ${paidOrders}\n` +
        `🎯 Trial Orders: ${trialOrders}\n` +
        `💰 Total Revenue: $${totalRevenue.toFixed(2)}\n` +
        `📈 Conversion Rate: ${totalOrders > 0 ? ((paidOrders / totalOrders) * 100).toFixed(1) : 0}%\n\n` +
        `🕐 Generated: ${new Date().toLocaleString()}`;

      await this.sendMessage(message);
    } catch (error) {
      await this.sendMessage('❌ Failed to fetch statistics.');
    }
  }

  /**
   * Handle mark as paid request
   */
  private async handleMarkAsPaid(orderId: number): Promise<void> {
    try {
      console.log('🔄 Processing mark as paid for order:', orderId);

      // Get the order first
      const order = await storage.getInvoice(orderId);
      console.log('📋 Retrieved order:', order);
      if (!order) {
        await this.sendMessage('❌ Order not found.');
        return;
      }

      if (order.status === 'paid') {
        await this.sendMessage('✅ This order is already marked as paid.');
        return;
      }

      // Update the order status to paid
      const updatedOrder = await storage.updateInvoice(orderId, {
        status: 'paid'
      });

      if (updatedOrder) {
        // Automatically add client email to Allowed Emails when marking as paid
        try {
          console.log('🔍 Adding email to Allowed Emails (Mark as Paid):', order.customerEmail);

          // For testing: Add a sample email subject to Order #5 if it doesn't have one
          if (orderId === 5 && (!order.notes || !order.notes.includes('Email sent on'))) {
            console.log('🧪 Adding test email subject to Order #5 for testing...');
            const testEmailSubject = 'Your IPTV Subscription Details - Template Subject Test';
            const testNotes = (order.notes || '') + `\n\nEmail sent on 2025-05-28T12:00:00.000Z - Subject: ${testEmailSubject}`;
            await storage.updateInvoice(orderId, { notes: testNotes });
            order.notes = testNotes;
            console.log('🧪 Test email subject added to Order #5');
          }

          // Get checkout page name if available
          let checkoutPageName = 'N/A';
          if (order.customCheckoutPageId) {
            try {
              const checkoutPage = await storage.getCustomCheckoutPage(order.customCheckoutPageId);
              if (checkoutPage) {
                checkoutPageName = checkoutPage.title;
              }
            } catch (error) {
              console.log('⚠️ Could not fetch checkout page name:', error);
            }
          }

          // Format date as DD/MM/YYYY
          const currentDate = new Date();
          const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()}`;

          // Build extraction notes with the exact format requested
          let extractionNotes = '';
          extractionNotes += `Customer: ${order.customerName || 'N/A'}\n`;
          extractionNotes += `Country: ${order.country || 'N/A'}\n`;
          extractionNotes += `Checkout Page: ${checkoutPageName}\n`;
          extractionNotes += `App: ${order.appType || 'N/A'}\n`;
          extractionNotes += `Date: ${formattedDate}`;

          console.log('📝 Extraction notes to be added:', extractionNotes);

          // Extract the last email subject from order notes if available
          let lastEmailSubject = `Order #${orderId} - Payment Confirmed`; // Default fallback

          if (order.notes) {
            // Look for the most recent email subject in the notes
            // Pattern: "Email sent on [timestamp] - Subject: [subject]"
            const emailSubjectMatches = order.notes.match(/Email sent on .+ - Subject: (.+)/g);
            if (emailSubjectMatches && emailSubjectMatches.length > 0) {
              // Get the last (most recent) email subject
              const lastMatch = emailSubjectMatches[emailSubjectMatches.length - 1];
              const subjectMatch = lastMatch.match(/Subject: (.+)$/);
              if (subjectMatch && subjectMatch[1]) {
                lastEmailSubject = subjectMatch[1].trim();
                console.log('📧 Found last email subject in notes:', lastEmailSubject);
              }
            }
          }

          const allowedEmailData = {
            notes: extractionNotes.trim(),
            lastSubject: lastEmailSubject,
            smtpProvider: 'Telegram Bot - Mark as Paid',
            lastUpdated: new Date().toISOString()
          };

          console.log('💾 Allowed email data:', allowedEmailData);

          const result = await storage.updateOrCreateAllowedEmail(order.customerEmail, allowedEmailData);
          console.log('✅ Allowed email update result:', result);

          let message = `✅ <b>Order #${orderId} Marked as Paid!</b>\n\n`;
          message += `👤 <b>Customer:</b> ${order.customerName}\n`;
          message += `📧 <b>Email:</b> ${order.customerEmail}\n`;
          message += `💰 <b>Amount:</b> $${order.amount}\n`;
          message += `📅 <b>Updated:</b> ${new Date().toLocaleString()}\n\n`;
          message += `🎉 <b>Payment status successfully updated!</b>\n`;
          message += `📝 <b>Email automatically added to Allowed Emails</b>`;

          await this.sendMessage(message);
        } catch (allowedEmailError) {
          console.error('❌ Error adding email to allowed list:', allowedEmailError);

          let message = `✅ <b>Order #${orderId} Marked as Paid!</b>\n\n`;
          message += `👤 <b>Customer:</b> ${order.customerName}\n`;
          message += `📧 <b>Email:</b> ${order.customerEmail}\n`;
          message += `💰 <b>Amount:</b> $${order.amount}\n`;
          message += `📅 <b>Updated:</b> ${new Date().toLocaleString()}\n\n`;
          message += `🎉 <b>Payment status successfully updated!</b>\n`;
          message += `⚠️ <b>Note:</b> Could not add email to Allowed Emails`;

          await this.sendMessage(message);
        }
      } else {
        await this.sendMessage('❌ Failed to update order status.');
      }
    } catch (error) {
      console.error('Error marking order as paid:', error);
      await this.sendMessage('❌ Failed to mark order as paid.');
    }
  }

  /**
   * Security Features
   */

  /**
   * Send login alert notification
   */
  async sendLoginAlert(username: string, ip: string, userAgent: string, success: boolean, sessionId?: string): Promise<void> {
    try {
      if (!this.config.enabled) return;

      const timestamp = new Date().toLocaleString();
      const status = success ? '✅ SUCCESS' : '❌ FAILED';
      const statusEmoji = success ? '🔓' : '🚨';

      let message = `${statusEmoji} <b>Admin Login Alert</b>\n\n`;
      message += `👤 <b>Username:</b> ${username}\n`;
      message += `🌐 <b>IP Address:</b> ${ip}\n`;
      message += `📱 <b>User Agent:</b> ${userAgent.substring(0, 50)}...\n`;
      message += `🕐 <b>Time:</b> ${timestamp}\n`;
      message += `📊 <b>Status:</b> ${status}\n`;

      if (success && sessionId) {
        message += `\n🔑 <b>Session ID:</b> ${sessionId.substring(0, 8)}...`;
      }

      await this.sendMessage(message);
    } catch (error) {
      console.error('Error sending login alert:', error);
    }
  }

  /**
   * Request login approval for new IP
   */
  async requestLoginApproval(username: string, ip: string, userAgent: string, sessionId: string): Promise<string> {
    try {
      if (!this.config.enabled) return 'approved'; // Auto-approve if bot disabled

      const approvalId = `login_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Store pending approval
      this.pendingLoginApprovals.set(approvalId, {
        username,
        ip,
        userAgent,
        timestamp: Date.now(),
        sessionId
      });

      // Auto-expire after 5 minutes
      setTimeout(() => {
        this.pendingLoginApprovals.delete(approvalId);
      }, 5 * 60 * 1000);

      const timestamp = new Date().toLocaleString();

      let message = `🚨 <b>New IP Login Attempt</b>\n\n`;
      message += `👤 <b>Username:</b> ${username}\n`;
      message += `🌐 <b>IP Address:</b> ${ip}\n`;
      message += `📱 <b>User Agent:</b> ${userAgent.substring(0, 50)}...\n`;
      message += `🕐 <b>Time:</b> ${timestamp}\n\n`;
      message += `⚠️ <b>This IP address is not recognized.</b>\n`;
      message += `Do you want to approve this login?`;

      const replyMarkup = {
        inline_keyboard: [
          [
            { text: '✅ Approve', callback_data: `approve_login_${approvalId}` },
            { text: '❌ Deny', callback_data: `deny_login_${approvalId}` }
          ]
        ]
      };

      await this.sendMessage(message, replyMarkup);
      return approvalId;
    } catch (error) {
      console.error('Error requesting login approval:', error);
      return 'approved'; // Auto-approve on error
    }
  }

  /**
   * Handle login approval response
   */
  private async handleLoginApproval(approvalId: string, approved: boolean): Promise<void> {
    try {
      const pendingApproval = this.pendingLoginApprovals.get(approvalId);

      if (!pendingApproval) {
        await this.sendMessage('❌ Login approval request expired or not found.');
        return;
      }

      this.pendingLoginApprovals.delete(approvalId);

      const status = approved ? 'APPROVED' : 'DENIED';
      const emoji = approved ? '✅' : '❌';

      let message = `${emoji} <b>Login ${status}</b>\n\n`;
      message += `👤 <b>Username:</b> ${pendingApproval.username}\n`;
      message += `🌐 <b>IP:</b> ${pendingApproval.ip}\n`;
      message += `📅 <b>Decision Time:</b> ${new Date().toLocaleString()}\n`;

      if (approved) {
        message += `\n🔓 <b>Access granted.</b> User can now login.`;
        // Store approved IP for future reference
        await this.storeApprovedIP(pendingApproval.ip, pendingApproval.username);
      } else {
        message += `\n🚫 <b>Access denied.</b> Login attempt blocked.`;
      }

      await this.sendMessage(message);

      // Notify the login system about the decision
      this.notifyLoginDecision(approvalId, approved);
    } catch (error) {
      console.error('Error handling login approval:', error);
      await this.sendMessage('❌ Error processing login approval.');
    }
  }

  /**
   * Request 2FA approval via Telegram
   */
  async request2FAApproval(username: string, sessionId: string): Promise<string> {
    try {
      if (!this.config.enabled) return 'approved'; // Auto-approve if bot disabled

      const requestId = `2fa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Store pending 2FA request
      this.pending2FARequests.set(requestId, {
        username,
        sessionId,
        timestamp: Date.now()
      });

      // Auto-expire after 2 minutes
      setTimeout(() => {
        this.pending2FARequests.delete(requestId);
      }, 2 * 60 * 1000);

      const timestamp = new Date().toLocaleString();

      let message = `🔐 <b>Two-Factor Authentication</b>\n\n`;
      message += `👤 <b>Username:</b> ${username}\n`;
      message += `🕐 <b>Time:</b> ${timestamp}\n`;
      message += `🔑 <b>Session:</b> ${sessionId.substring(0, 8)}...\n\n`;
      message += `🛡️ <b>Approve this login attempt?</b>\n`;
      message += `⏰ <i>This request expires in 2 minutes.</i>`;

      const replyMarkup = {
        inline_keyboard: [
          [
            { text: '✅ Approve', callback_data: `approve_2fa_${requestId}` },
            { text: '❌ Deny', callback_data: `deny_2fa_${requestId}` }
          ]
        ]
      };

      await this.sendMessage(message, replyMarkup);
      return requestId;
    } catch (error) {
      console.error('Error requesting 2FA approval:', error);
      return 'approved'; // Auto-approve on error
    }
  }

  /**
   * Handle 2FA approval response
   */
  private async handle2FAApproval(requestId: string, approved: boolean): Promise<void> {
    try {
      const pendingRequest = this.pending2FARequests.get(requestId);

      if (!pendingRequest) {
        await this.sendMessage('❌ 2FA request expired or not found.');
        return;
      }

      this.pending2FARequests.delete(requestId);

      const status = approved ? 'APPROVED' : 'DENIED';
      const emoji = approved ? '✅' : '❌';

      let message = `${emoji} <b>2FA ${status}</b>\n\n`;
      message += `👤 <b>Username:</b> ${pendingRequest.username}\n`;
      message += `📅 <b>Decision Time:</b> ${new Date().toLocaleString()}\n`;

      if (approved) {
        message += `\n🔓 <b>Authentication successful.</b> Access granted.`;
      } else {
        message += `\n🚫 <b>Authentication denied.</b> Access blocked.`;
      }

      await this.sendMessage(message);

      // Notify the login system about the 2FA decision
      this.notify2FADecision(requestId, approved);
    } catch (error) {
      console.error('Error handling 2FA approval:', error);
      await this.sendMessage('❌ Error processing 2FA approval.');
    }
  }

  /**
   * Check if IP is approved
   */
  async isIPApproved(ip: string): Promise<boolean> {
    try {
      // For now, we'll use a simple in-memory store
      // In production, this should be stored in a database
      const approvedIPs = await this.getApprovedIPs();
      return approvedIPs.includes(ip);
    } catch (error) {
      console.error('Error checking IP approval:', error);
      return false;
    }
  }

  /**
   * Store approved IP
   */
  private async storeApprovedIP(ip: string, username: string): Promise<void> {
    try {
      // For now, we'll use a simple in-memory store
      // In production, this should be stored in a database
      const approvedIPs = await this.getApprovedIPs();
      if (!approvedIPs.includes(ip)) {
        approvedIPs.push(ip);
        await this.saveApprovedIPs(approvedIPs);
        console.log(`✅ IP ${ip} approved for user ${username}`);
      }
    } catch (error) {
      console.error('Error storing approved IP:', error);
    }
  }

  /**
   * Get approved IPs (placeholder - should be database in production)
   */
  private async getApprovedIPs(): Promise<string[]> {
    // This is a placeholder - in production, this should be stored in database
    return ['127.0.0.1', '::1']; // Always allow localhost
  }

  /**
   * Save approved IPs (placeholder - should be database in production)
   */
  private async saveApprovedIPs(ips: string[]): Promise<void> {
    // This is a placeholder - in production, this should be stored in database
    console.log('Approved IPs updated:', ips);
  }

  /**
   * Notify login system about approval decision
   */
  private notifyLoginDecision(approvalId: string, approved: boolean): void {
    // This will be used by the login system to check approval status
    // We'll implement this as an event emitter or callback system
    console.log(`Login decision for ${approvalId}: ${approved ? 'APPROVED' : 'DENIED'}`);
  }

  /**
   * Notify login system about 2FA decision
   */
  private notify2FADecision(requestId: string, approved: boolean): void {
    // This will be used by the login system to check 2FA status
    console.log(`2FA decision for ${requestId}: ${approved ? 'APPROVED' : 'DENIED'}`);
  }

  /**
   * Get pending approval status
   */
  getLoginApprovalStatus(approvalId: string): 'pending' | 'approved' | 'denied' | 'expired' {
    const approval = this.pendingLoginApprovals.get(approvalId);
    if (!approval) return 'expired';
    return 'pending';
  }

  /**
   * Get pending 2FA status
   */
  get2FAStatus(requestId: string): 'pending' | 'approved' | 'denied' | 'expired' {
    const request = this.pending2FARequests.get(requestId);
    if (!request) return 'expired';
    return 'pending';
  }

  /**
   * Handle /system command - Get system performance report
   */
  private async handleSystemCommand(): Promise<void> {
    try {
      const { systemMonitor } = await import('./system-monitor');
      const report = await systemMonitor.getSystemReport();
      await this.sendMessage(report);
    } catch (error) {
      await this.sendMessage('❌ Failed to get system report. System monitoring may not be available.');
    }
  }

  /**
   * Handle /monitor command - Toggle system monitoring
   */
  private async handleMonitorCommand(): Promise<void> {
    try {
      const { systemMonitor } = await import('./system-monitor');

      if (systemMonitor.isActive) {
        systemMonitor.stopMonitoring();
        await this.sendMessage('🛑 **System monitoring stopped**\n\nMonitoring has been disabled. Use `/monitor` again to restart.');
      } else {
        await systemMonitor.startMonitoring(5); // 5 minute intervals
        await this.sendMessage('🔍 **System monitoring started**\n\nMonitoring every 5 minutes. You will receive alerts for:\n• High CPU usage (>80%)\n• High memory usage (>85%)\n• High disk usage (>90%)\n• Critical errors');
      }
    } catch (error) {
      await this.sendMessage('❌ Failed to toggle system monitoring. Please check server logs.');
    }
  }

  /**
   * Handle /errors command - View recent error logs
   */
  private async handleErrorsCommand(): Promise<void> {
    try {
      const { systemMonitor } = await import('./system-monitor');
      const errors = systemMonitor.recentErrors;

      if (errors.length === 0) {
        await this.sendMessage('✅ **No Recent Errors**\n\nNo errors have been logged recently. System is running smoothly!');
        return;
      }

      let message = `🚨 **Recent Error Logs (${errors.length})**\n\n`;

      errors.forEach((error, index) => {
        const emoji = error.level === 'critical' ? '🔴' : error.level === 'error' ? '🟠' : '🟡';
        message += `${emoji} **${error.level.toUpperCase()}**\n`;
        message += `📝 ${error.message.substring(0, 100)}${error.message.length > 100 ? '...' : ''}\n`;
        message += `🔧 Source: ${error.source}\n`;
        message += `⏰ ${new Date(error.timestamp).toLocaleString()}\n\n`;
      });

      await this.sendMessage(message);
    } catch (error) {
      await this.sendMessage('❌ Failed to get error logs. System monitoring may not be available.');
    }
  }
}

// Export singleton instance
export const telegramBot = new TelegramBotService();

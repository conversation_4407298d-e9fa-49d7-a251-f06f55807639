/**
 * Email Templates Configuration
 * This file defines the structure and default values for email templates
 * that can be customized through the admin interface.
 */

export interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  category: string;
  isDefault: boolean;
}

export interface EmailTemplateCategory {
  id: string;
  name: string;
  description: string;
}

export const EMAIL_TEMPLATE_CATEGORIES: EmailTemplateCategory[] = [
  {
    id: 'purchase',
    name: 'Purchase Emails',
    description: 'Emails sent when a customer makes a purchase'
  },
  {
    id: 'trial',
    name: 'Trial Emails',
    description: 'Emails sent for trial subscriptions'
  },
  {
    id: 'iptv',
    name: 'IPTV & Streaming',
    description: 'Emails for IPTV subscriptions and streaming services'
  },
  {
    id: 'notification',
    name: 'Notification Emails',
    description: 'General notification emails'
  },
  {
    id: 'marketing',
    name: 'Marketing Emails',
    description: 'Emails for marketing campaigns'
  },
  {
    id: 'support',
    name: 'Support Emails',
    description: 'Emails for customer support'
  }
];

// Default email templates - Only Smartonn template
export const DEFAULT_EMAIL_TEMPLATES: EmailTemplate[] = [
  // IPTV Subscription Email - Smartonn Template
  {
    id: 'smartonn_template',
    name: 'Smartonn',
    description: 'IPTV subscription email with credentials and installation links',
    subject: 'Your IPTV Subscription Details - {{customerName}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #333; margin-bottom: 10px;">Your IPTV Subscription is Ready!</h1>
          <p style="color: #666; font-size: 16px;">Order #{{orderId}}</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
          <p style="font-size: 16px; line-height: 1.5;">Here is your subscription as an M3U link:</p>
          <p style="font-size: 16px; line-height: 1.5;">{{m3uLink}}</p>
        </div>

        <div style="border-top: 2px solid #333; border-bottom: 2px solid #333; padding: 20px; margin: 20px 0;">
          <p style="margin: 10px 0;"><strong>Username:</strong> {{username}}</p>
          <p style="margin: 10px 0;"><strong>Password:</strong> {{password}}</p>
          <p style="margin: 10px 0;"><strong>Lien:</strong> <a href="http://premium.pro4ott.com:8789" style="color: #007bff;">http://premium.pro4ott.com:8789</a></p>
          <p style="margin: 10px 0;"><strong>Lien 2:</strong> <a href="http://live.mypythontv.com:2052" style="color: #007bff;">http://live.mypythontv.com:2052</a></p>
          <p style="margin: 10px 0;"><strong>Lien 3:</strong> <a href="http://mypythonpremium.com:2052" style="color: #007bff;">http://mypythonpremium.com:2052</a></p>
        </div>

        <div style="border-top: 2px solid #333; border-bottom: 2px solid #333; padding: 20px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">📱 Installation</h3>
          <p style="margin: 10px 0;"><a href="https://smartonn.com/install/" style="color: #007bff; text-decoration: none; font-weight: bold;">https://smartonn.com/install/</a></p>
        </div>

        <div style="border-top: 2px solid #333; border-bottom: 2px solid #333; padding: 20px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">📧 Support</h3>
          <p style="margin: 10px 0;">If you have any problems contact us at</p>
          <p style="margin: 10px 0;"><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #007bff;"><EMAIL></a></p>
        </div>

        <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #495057;">Thank you for choosing our IPTV service!</p>
          <p style="margin: 5px 0 0 0; color: #495057;"><strong>Smartonn Team</strong></p>
        </div>
      </div>
    `,
    textContent: `Your IPTV Subscription Details - Order #{{orderId}}

Dear {{customerName}},

Here is your subscription as an M3U link:
{{m3uLink}}

———————————————

Username: {{username}}
Password: {{password}}
Lien: http://premium.pro4ott.com:8789
Lien 2: http://live.mypythontv.com:2052
Lien 3: http://mypythonpremium.com:2052

———————————————

Installation: https://smartonn.com/install/

———————————————

If you have any problems contact us at
Email: <EMAIL>

Thank you for choosing our IPTV service!
Smartonn Team`,
    category: 'iptv',
    isDefault: true
  }
];

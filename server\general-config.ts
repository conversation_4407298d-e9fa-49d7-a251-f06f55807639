// General settings interfaces
interface DefaultTestCustomer {
  enabled: boolean;
  name: string;
  email: string;
}

interface EmailDomainRestriction {
  enabled: boolean;
  allowedDomains: string;
}

interface SEOPrivacySettings {
  globalNoIndex: boolean;
  hideFromSearchEngines: boolean;
  disableSitemaps: boolean;
  hideFramework: boolean;
  customRobotsTxt: string;
  pageIndexingRules: {
    homepage: boolean;
    checkoutPages: boolean;
    adminPages: boolean;
    customPages: boolean;
  };
  privacyHeaders: {
    hideServerInfo: boolean;
    preventFraming: boolean;
    disableReferrer: boolean;
    hideGenerator: boolean;
  };
}

interface TelegramBotSettings {
  enabled: boolean;
  botToken: string;
  adminChatId: string;
  webhookUrl: string;
  notifications: {
    newOrders: boolean;
    paymentConfirmations: boolean;
    trialUpgrades: boolean;
    orderStatusChanges: boolean;
  };
  emailIntegration: {
    enabled: boolean;
    allowQuickSend: boolean;
    defaultTemplateId: string;
  };
  m3uManagement: {
    enabled: boolean;
    autoExtractCredentials: boolean;
    credentialFormat: string;
    defaultM3uLinks: string[];
  };
  security: {
    verifyAdminOnly: boolean;
    rateLimitEnabled: boolean;
    auditLogging: boolean;
  };
}

export interface GeneralConfig {
  siteName: string;
  siteDescription: string;
  logoUrl: string;
  faviconUrl: string;
  primaryColor: string;
  secondaryColor: string;
  footerText: string;
  enableCheckout: boolean;
  enableCustomCheckout: boolean;
  enableTestMode: boolean;
  defaultTestCustomer: DefaultTestCustomer;
  emailDomainRestriction: EmailDomainRestriction;
  seoPrivacy: SEOPrivacySettings;
  telegramBot: TelegramBotSettings;
}

// In-memory configuration storage for demo
export const generalConfigStorage: GeneralConfig = {
  siteName: "🚀 TESTING - TemplateHub Pro",
  siteDescription: "🎯 TESTING - Premium productivity app templates and UI/UX design systems",
  logoUrl: "",
  faviconUrl: "",
  primaryColor: "#ff6b6b",
  secondaryColor: "#4ecdc4",
  footerText: "© 2024 🚀 TESTING - TemplateHub Pro",
  enableCheckout: true,
  enableCustomCheckout: true,
  enableTestMode: true,
  defaultTestCustomer: {
    enabled: true,
    name: "Test Designer",
    email: "<EMAIL>"
  },
  emailDomainRestriction: {
    enabled: false,
    allowedDomains: "gmail.com, hotmail.com, yahoo.com"
  },
  seoPrivacy: {
    globalNoIndex: true,
    hideFromSearchEngines: true,
    disableSitemaps: true,
    hideFramework: true,
    customRobotsTxt: `User-agent: *
Disallow: /

# Block all search engines and crawlers
User-agent: Googlebot
Disallow: /

User-agent: Bingbot
Disallow: /

User-agent: Slurp
Disallow: /

User-agent: DuckDuckBot
Disallow: /

User-agent: Baiduspider
Disallow: /

User-agent: YandexBot
Disallow: /

User-agent: facebookexternalhit
Disallow: /

User-agent: Twitterbot
Disallow: /

User-agent: LinkedInBot
Disallow: /

User-agent: WhatsApp
Disallow: /

User-agent: TelegramBot
Disallow: /

# Block SEO tools and analyzers
User-agent: AhrefsBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: BLEXBot
Disallow: /

User-agent: SiteAuditBot
Disallow: /

User-agent: MegaIndex
Disallow: /

User-agent: ScreamingFrogSEOSpider
Disallow: /

User-agent: SeoSiteCheckup
Disallow: /

User-agent: WooRankBot
Disallow: /

User-agent: SEOkicks
Disallow: /

User-agent: SEOlyticsCrawler
Disallow: /

User-agent: LinkdexBot
Disallow: /

User-agent: spbot
Disallow: /

User-agent: MojeekBot
Disallow: /

User-agent: PetalBot
Disallow: /

User-agent: CCBot
Disallow: /

User-agent: GPTBot
Disallow: /

User-agent: ChatGPT-User
Disallow: /

User-agent: Claude-Web
Disallow: /

User-agent: anthropic-ai
Disallow: /

User-agent: PerplexityBot
Disallow: /

# No sitemap provided
# Sitemap:`,
    pageIndexingRules: {
      homepage: false,
      checkoutPages: false,
      adminPages: false,
      customPages: false
    },
    privacyHeaders: {
      hideServerInfo: true,
      preventFraming: true,
      disableReferrer: true,
      hideGenerator: true
    }
  },
  telegramBot: {
    enabled: true,
    botToken: '8136736828:AAFz4hZMSslpUAZvJPO0MSzwiyYSIhQVeyk',
    adminChatId: '383368242',
    webhookUrl: 'https://00d6-196-74-45-154.ngrok-free.app/api/telegram/webhook',
    notifications: {
      newOrders: true,
      paymentConfirmations: true,
      trialUpgrades: true,
      orderStatusChanges: true
    },
    emailIntegration: {
      enabled: true,
      allowQuickSend: true,
      defaultTemplateId: 'smartonn_template'
    },
    m3uManagement: {
      enabled: true,
      autoExtractCredentials: true,
      credentialFormat: 'Username: {username}\nPassword: {password}\nM3U URL: {m3u_url}',
      defaultM3uLinks: []
    },
    security: {
      verifyAdminOnly: true,
      rateLimitEnabled: true,
      auditLogging: true
    }
  }
};

/**
 * Get the general configuration from storage
 */
export function getGeneralConfig(): GeneralConfig {
  return generalConfigStorage;
}

/**
 * Update the general configuration in storage
 */
export function updateGeneralConfig(config: Partial<GeneralConfig>): GeneralConfig {
  Object.assign(generalConfigStorage, config);
  return generalConfigStorage;
}

/**
 * Get the allowed email domains as an array
 */
export function getAllowedEmailDomains(): string[] {
  const { enabled, allowedDomains } = generalConfigStorage.emailDomainRestriction;

  if (!enabled || !allowedDomains) {
    return [];
  }

  return allowedDomains
    .split(',')
    .map(domain => domain.trim())
    .filter(domain => domain.length > 0);
}

/**
 * Check if email domain restriction is enabled
 */
export function isEmailDomainRestrictionEnabled(): boolean {
  return generalConfigStorage.emailDomainRestriction.enabled;
}

/**
 * Get the default test customer
 */
export function getDefaultTestCustomer(): DefaultTestCustomer | null {
  const { enabled, name, email } = generalConfigStorage.defaultTestCustomer;

  if (!enabled) {
    return null;
  }

  return { enabled, name, email };
}

/**
 * Check if test mode is enabled
 */
export function isTestModeEnabled(): boolean {
  return generalConfigStorage.enableTestMode;
}

/**
 * Check if checkout is enabled
 */
export function isCheckoutEnabled(): boolean {
  return generalConfigStorage.enableCheckout;
}

/**
 * Check if custom checkout is enabled
 */
export function isCustomCheckoutEnabled(): boolean {
  return generalConfigStorage.enableCustomCheckout;
}

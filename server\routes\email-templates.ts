import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { insertEmailTemplateSchema } from '@shared/schema';
import { DEFAULT_EMAIL_TEMPLATES, EmailTemplate, EMAIL_TEMPLATE_CATEGORIES } from '../../shared/email-templates';

export const emailTemplatesRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session for email templates:', req.session);
  if (req.session && req.session.isAdmin) {
    console.log('Admin session verified for email templates:', req.session.isAdmin);
    next();
  } else {
    console.log('Admin session verification failed for email templates');
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Initialize default email templates if none exist
const initializeDefaultTemplates = async () => {
  try {
    const existingTemplates = await storage.getEmailTemplates();

    if (existingTemplates.length === 0) {
      console.log('Initializing default email templates...');

      for (const template of DEFAULT_EMAIL_TEMPLATES) {
        await storage.createEmailTemplate({
          name: template.name,
          description: template.description || '',
          subject: template.subject,
          htmlContent: template.htmlContent,
          textContent: template.textContent || '',
          category: template.category,
          isDefault: template.isDefault,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }

      console.log(`Initialized ${DEFAULT_EMAIL_TEMPLATES.length} default email templates`);
    } else {
      console.log(`Found ${existingTemplates.length} existing email templates`);
    }
  } catch (error) {
    console.error('Error initializing default email templates:', error);
  }
};

// Call initialization function
initializeDefaultTemplates();

// Get all email templates
emailTemplatesRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const templates = await storage.getEmailTemplates();
    res.json(templates);
  } catch (error) {
    console.error('Error fetching email templates:', error);
    res.status(500).json({ message: 'Failed to fetch email templates' });
  }
});

// Get email templates by category
emailTemplatesRouter.get('/category/:category', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { category } = req.params;
    const templates = await storage.getEmailTemplates();

    const filteredTemplates = templates.filter(template => template.category === category);

    res.json(filteredTemplates);
  } catch (error) {
    console.error('Error fetching email templates by category:', error);
    res.status(500).json({ message: 'Failed to fetch email templates by category' });
  }
});

// Get email template categories
emailTemplatesRouter.get('/categories', checkAdmin, async (req: Request, res: Response) => {
  try {
    res.json(EMAIL_TEMPLATE_CATEGORIES);
  } catch (error) {
    console.error('Error fetching email template categories:', error);
    res.status(500).json({ message: 'Failed to fetch email template categories' });
  }
});

// Get a specific email template
emailTemplatesRouter.get('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    const template = await storage.getEmailTemplate(id);
    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }

    res.json(template);
  } catch (error) {
    console.error('Error fetching email template:', error);
    res.status(500).json({ message: 'Failed to fetch email template' });
  }
});

// Create a new email template
emailTemplatesRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    // Validate the request body
    const templateData = {
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const validatedData = insertEmailTemplateSchema.parse(templateData);

    // Create the template
    const template = await storage.createEmailTemplate(validatedData);

    res.status(201).json(template);
  } catch (error) {
    console.error('Error creating email template:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create email template' });
  }
});

// Update an email template
emailTemplatesRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    // Check if the template exists
    const existingTemplate = await storage.getEmailTemplate(id);
    if (!existingTemplate) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Update the template
    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    const updatedTemplate = await storage.updateEmailTemplate(id, updateData);

    res.json(updatedTemplate);
  } catch (error) {
    console.error('Error updating email template:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update email template' });
  }
});

// Delete an email template
emailTemplatesRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid template ID' });
    }

    // Check if the template exists
    const existingTemplate = await storage.getEmailTemplate(id);
    if (!existingTemplate) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Check if it's a default template
    if (existingTemplate.isDefault) {
      return res.status(400).json({ message: 'Cannot delete default email template' });
    }

    // Delete the template
    const success = await storage.deleteEmailTemplate(id);

    if (success) {
      res.json({ message: 'Template deleted successfully' });
    } else {
      res.status(500).json({ message: 'Failed to delete template' });
    }
  } catch (error) {
    console.error('Error deleting email template:', error);
    res.status(500).json({ message: 'Failed to delete email template' });
  }
});

// Preview an email template with test data
emailTemplatesRouter.post('/preview', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { templateId, testData } = req.body;

    if (!templateId) {
      return res.status(400).json({ message: 'Template ID is required' });
    }

    // Find the template
    const template = await storage.getEmailTemplate(parseInt(templateId));

    if (!template) {
      return res.status(404).json({ message: 'Email template not found' });
    }

    // Replace placeholders with test data
    let htmlContent = template.htmlContent;
    let textContent = template.textContent || '';
    let subject = template.subject;

    // Simple placeholder replacement
    if (testData) {
      Object.keys(testData).forEach(key => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        htmlContent = htmlContent.replace(regex, testData[key]);
        textContent = textContent.replace(regex, testData[key]);
        subject = subject.replace(regex, testData[key]);
      });
    }

    res.json({
      subject,
      htmlContent,
      textContent
    });
  } catch (error) {
    console.error('Error previewing email template:', error);
    res.status(500).json({ message: 'Failed to preview email template' });
  }
});
